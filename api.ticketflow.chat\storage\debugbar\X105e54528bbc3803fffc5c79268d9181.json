{"__meta": {"id": "X105e54528bbc3803fffc5c79268d9181", "datetime": "2025-07-24 23:17:42", "utime": 1753409862.298471, "method": "POST", "uri": "/api/v1/dashboard/user/cart/calculate/12?lang=pt-BR", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[23:17:42] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753409862.151813, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753409861.909066, "end": 1753409862.298493, "duration": 0.38942694664001465, "duration_str": "389ms", "measures": [{"label": "Booting", "start": 1753409861.909066, "relative_start": 0, "end": 1753409862.135635, "relative_end": 1753409862.135635, "duration": 0.22656893730163574, "duration_str": "227ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753409862.135645, "relative_start": 0.226578950881958, "end": 1753409862.298495, "relative_end": 2.1457672119140625e-06, "duration": 0.16285014152526855, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 44180144, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/dashboard/user/cart/calculate/{id}", "middleware": "api, block.ip, sanctum.check", "controller": "App\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController@cartCalculate", "as": "user.", "namespace": null, "prefix": "api/v1/dashboard/user", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php&line=235\">\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php:235-246</a>"}, "queries": {"nb_statements": 35, "nb_failed_statements": 0, "accumulated_duration": 0.01626, "accumulated_duration_str": "16.26ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0025, "duration_str": "2.5ms", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 0, "width_percent": 15.375}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 15.375, "width_percent": 2.03}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 17.405, "width_percent": 1.907}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 19.311, "width_percent": 1.845}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '25' limit 1", "type": "query", "params": [], "bindings": ["25"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 64}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php:64", "connection": "foodyman", "start_percent": 21.156, "width_percent": 2.706}, {"sql": "select * from `users` where `users`.`id` = 109 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["109"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 137}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:137", "connection": "foodyman", "start_percent": 23.862, "width_percent": 2.706}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-24 23:17:42', `personal_access_tokens`.`updated_at` = '2025-07-24 23:17:42' where `id` = 25", "type": "query", "params": [], "bindings": ["2025-07-24 23:17:42", "2025-07-24 23:17:42", "25"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 21, "namespace": "middleware", "name": "block.ip", "line": 24}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:83", "connection": "foodyman", "start_percent": 26.568, "width_percent": 5.72}, {"sql": "select count(*) as aggregate from `currencies` where `id` = 1 and `deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 32.288, "width_percent": 2.768}, {"sql": "select `carts`.*, (select count(*) from `user_carts` where `carts`.`id` = `user_carts`.`cart_id`) as `user_carts_count` from `carts` where `carts`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0005, "duration_str": "500μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 35.055, "width_percent": 3.075}, {"sql": "select `id`, `location`, `tax`, `price`, `price_per_km`, `uuid`, `logo_img`, `status` from `shops` where `shops`.`id` in (501) and `shops`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 38.13, "width_percent": 1.845}, {"sql": "select * from `shop_translations` where `shop_translations`.`shop_id` in (501) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `shop_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 39.975, "width_percent": 2.706}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_id` in (501) and `bonuses`.`bonusable_type` = 'App\\Models\\Shop' and `expired_at` > '2025-07-24 23:17:42' and `status` = 1 and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "2025-07-24 23:17:42", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 42.681, "width_percent": 1.968}, {"sql": "select * from `user_carts` where `user_carts`.`cart_id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 44.649, "width_percent": 1.661}, {"sql": "select * from `cart_details` where `cart_details`.`user_cart_id` in (12) and `parent_id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 46.31, "width_percent": 2.03}, {"sql": "select * from `stocks` where `stocks`.`id` in (3, 4) and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 48.339, "width_percent": 1.907}, {"sql": "select * from `products` where `products`.`id` in (1, 2) and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 37, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 50.246, "width_percent": 1.968}, {"sql": "select * from `units` where `units`.`id` in (1) and `units`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 41, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 42, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00026000000000000003, "duration_str": "260μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 52.214, "width_percent": 1.599}, {"sql": "select * from `unit_translations` where `unit_translations`.`unit_id` in (1) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `unit_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 46, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 47, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 48, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 49, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 53.813, "width_percent": 2.03}, {"sql": "select * from `product_translations` where `product_translations`.`product_id` in (1, 2) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `product_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 41, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 42, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 55.843, "width_percent": 1.784}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` in (1, 2) and `start` <= '2025-07-24 00:00:00' and `end` >= '2025-07-24 00:00:00' and `active` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-07-24 00:00:00", "2025-07-24 00:00:00", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 40, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 41, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 57.626, "width_percent": 2.952}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_id` in (3, 4) and `bonuses`.`bonusable_type` = 'App\\Models\\Stock' and `expired_at` > '2025-07-24 23:17:42' and `status` = 1 and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Stock", "2025-07-24 23:17:42", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 37, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 60.578, "width_percent": 2.03}, {"sql": "select `extra_values`.*, `stock_extras`.`stock_id` as `pivot_stock_id`, `stock_extras`.`extra_value_id` as `pivot_extra_value_id` from `extra_values` inner join `stock_extras` on `extra_values`.`id` = `stock_extras`.`extra_value_id` where `stock_extras`.`stock_id` in (3, 4) and `extra_values`.`deleted_at` is null order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 35, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 36, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 62.608, "width_percent": 2.276}, {"sql": "select * from `cart_details` where `cart_details`.`parent_id` in (13, 14)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 124}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:124", "connection": "foodyman", "start_percent": 64.883, "width_percent": 2.091}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'carts'", "type": "query", "params": [], "bindings": ["foodyman", "carts"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 142}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:142", "connection": "foodyman", "start_percent": 66.974, "width_percent": 5.535}, {"sql": "select exists(select * from `users` where `phone` is not null and `id` is null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 286}, {"index": 10, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 148}, {"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:286", "connection": "foodyman", "start_percent": 72.509, "width_percent": 2.276}, {"sql": "select * from `settings` where `key` = 'before_order_phone_required' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["before_order_phone_required"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 288}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 148}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:288", "connection": "foodyman", "start_percent": 74.785, "width_percent": 2.645}, {"sql": "select * from `user_carts` where `user_carts`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\CartDetail.php", "line": 60}, {"index": 26, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 179}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00053, "duration_str": "530μs", "stmt_id": "\\app\\Models\\CartDetail.php:60", "connection": "foodyman", "start_percent": 77.429, "width_percent": 3.26}, {"sql": "select * from `carts` where `carts`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\CartDetail.php", "line": 60}, {"index": 26, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 179}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Models\\CartDetail.php:60", "connection": "foodyman", "start_percent": 80.689, "width_percent": 2.214}, {"sql": "select * from `user_carts` where `user_carts`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\CartDetail.php", "line": 60}, {"index": 26, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 179}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Models\\CartDetail.php:60", "connection": "foodyman", "start_percent": 82.903, "width_percent": 1.845}, {"sql": "select * from `carts` where `carts`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\CartDetail.php", "line": 60}, {"index": 26, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 179}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Models\\CartDetail.php:60", "connection": "foodyman", "start_percent": 84.748, "width_percent": 1.845}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 17, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 219}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 86.593, "width_percent": 2.829}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 17, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 219}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 89.422, "width_percent": 2.583}, {"sql": "select * from `receipts` where exists (select * from `stocks` inner join `receipt_stocks` on `stocks`.`id` = `receipt_stocks`.`stock_id` where `receipts`.`id` = `receipt_stocks`.`receipt_id` and `stocks`.`deleted_at` is null) and `shop_id` = 501 and `receipts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1350}, {"index": 15, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 219}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.0005600000000000001, "duration_str": "560μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1350", "connection": "foodyman", "start_percent": 92.005, "width_percent": 3.444}, {"sql": "select * from `settings` where `key` = 'service_fee' and `settings`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["service_fee"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 235}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:235", "connection": "foodyman", "start_percent": 95.449, "width_percent": 2.276}, {"sql": "select * from `coupons` where `name` is null and `shop_id` = 501 and `qty` > 0 and date(`expired_at`) > '2025-07-24' and `coupons`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["501", "0", "2025-07-24"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CartRepository\\CartRepository.php", "line": 238}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 237}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Repositories\\CartRepository\\CartRepository.php:238", "connection": "foodyman", "start_percent": 97.724, "width_percent": 2.276}]}, "models": {"data": {"App\\Models\\Settings": 1, "App\\Models\\ProductTranslation": 2, "App\\Models\\Unit": 1, "App\\Models\\Product": 2, "App\\Models\\Stock": 2, "App\\Models\\CartDetail": 2, "App\\Models\\UserCart": 3, "App\\Models\\ShopTranslation": 1, "App\\Models\\Shop": 1, "App\\Models\\Cart": 3, "App\\Models\\User": 1, "Laravel\\Sanctum\\PersonalAccessToken": 1, "App\\Models\\Currency": 3, "App\\Models\\Language": 3}, "count": 26}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f78c132-e99c-42e0-a8aa-6358e289227f\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/dashboard/user/cart/calculate/12", "status_code": "<pre class=sf-dump id=sf-dump-682530991 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-682530991\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-734374523 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-734374523\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1292721432 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>address</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>latitude</span>\" => \"<span class=sf-dump-str title=\"19 characters\">-18.439344666666663</span>\"\n    \"<span class=sf-dump-key>longitude</span>\" => \"<span class=sf-dump-str title=\"18 characters\">-50.43331116666666</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">delivery</span>\"\n  \"<span class=sf-dump-key>currency_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>receipt_discount</span>\" => <span class=sf-dump-num>0</span>\n  \"<span class=sf-dump-key>receipt_count</span>\" => <span class=sf-dump-num>0</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1292721432\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-490086376 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">113</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">Bearer 25|BDXbYGnUezbkTljZPgqRpjj1nzTxlsWEC4Q5o4YD</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-490086376\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1303569888 data-indent-pad=\"  \"><span class=sf-dump-note>array:39</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60983</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"51 characters\">/api/v1/dashboard/user/cart/calculate/12?lang=pt-BR</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"40 characters\">/api/v1/dashboard/user/cart/calculate/12</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"50 characters\">/index.php/api/v1/dashboard/user/cart/calculate/12</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lang=pt-BR</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">113</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">113</span>\"\n  \"<span class=sf-dump-key>HTTP_PRAGMA</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Bearer 25|BDXbYGnUezbkTljZPgqRpjj1nzTxlsWEC4Q5o4YD</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753409861.9091</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753409861</span>\n  \"<span class=sf-dump-key>argv</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">lang=pt-BR</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303569888\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-552197407 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-552197407\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1045202612 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 02:17:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4996</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045202612\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1540571620 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1540571620\", {\"maxDepth\":0})</script>\n"}}