"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./containers/layout/layout.tsx":
/*!**************************************!*\
  !*** ./containers/layout/layout.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Layout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _header_header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./header/header */ \"./containers/layout/header/header.tsx\");\n/* harmony import */ var _mobileHeader_mobileHeader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mobileHeader/mobileHeader */ \"./containers/layout/mobileHeader/mobileHeader.tsx\");\n/* harmony import */ var _profileHeader_profileHeader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./profileHeader/profileHeader */ \"./containers/layout/profileHeader/profileHeader.tsx\");\n/* harmony import */ var _mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @mui/material */ \"./node_modules/@mui/material/index.js\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var services_cart__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! services/cart */ \"./services/cart.ts\");\n/* harmony import */ var hooks_useRedux__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! hooks/useRedux */ \"./hooks/useRedux.tsx\");\n/* harmony import */ var redux_slices_userCart__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! redux/slices/userCart */ \"./redux/slices/userCart.ts\");\n/* harmony import */ var redux_slices_cart__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! redux/slices/cart */ \"./redux/slices/cart.ts\");\n/* harmony import */ var contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! contexts/auth/auth.context */ \"./contexts/auth/auth.context.tsx\");\n/* harmony import */ var redux_slices_currency__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! redux/slices/currency */ \"./redux/slices/currency.ts\");\n/* harmony import */ var services_currency__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! services/currency */ \"./services/currency.ts\");\n/* harmony import */ var services_language__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! services/language */ \"./services/language.ts\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var services_information__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! services/information */ \"./services/information.ts\");\n/* harmony import */ var contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! contexts/settings/settings.context */ \"./contexts/settings/settings.context.tsx\");\n/* harmony import */ var containers_errorBoundary_errorBoundary__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! containers/errorBoundary/errorBoundary */ \"./containers/errorBoundary/errorBoundary.tsx\");\n/* harmony import */ var contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! contexts/theme/theme.context */ \"./contexts/theme/theme.context.tsx\");\n/* harmony import */ var _footer_footer__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./footer/footer */ \"./containers/layout/footer/footer.tsx\");\n/* harmony import */ var services_translations__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! services/translations */ \"./services/translations.ts\");\n/* harmony import */ var hooks_useLocale__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! hooks/useLocale */ \"./hooks/useLocale.tsx\");\n/* harmony import */ var utils_createSettings__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! utils/createSettings */ \"./utils/createSettings.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! dayjs */ \"./node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var dayjs_locale_nl__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! dayjs/locale/nl */ \"./node_modules/dayjs/locale/nl.js\");\n/* harmony import */ var dayjs_locale_nl__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(dayjs_locale_nl__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var dayjs_locale_pl__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! dayjs/locale/pl */ \"./node_modules/dayjs/locale/pl.js\");\n/* harmony import */ var dayjs_locale_pl__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(dayjs_locale_pl__WEBPACK_IMPORTED_MODULE_26__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst PushNotification = next_dynamic__WEBPACK_IMPORTED_MODULE_15___default()(()=>__webpack_require__.e(/*! import() */ \"containers_pushNotification_pushNotification_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! containers/pushNotification/pushNotification */ \"./containers/pushNotification/pushNotification.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"..\\\\containers\\\\layout\\\\layout.tsx -> \" + \"containers/pushNotification/pushNotification\"\n        ]\n    }\n});\n_c = PushNotification;\nconst profileRoutes = [\n    \"checkout\",\n    \"profile\",\n    \"settings\",\n    \"help\",\n    \"orders/\",\n    \"be-seller\"\n];\nfunction Layout(param) {\n    let { children , locale  } = param;\n    _s();\n    const { pathname  } = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { addResourceBundle  } = (0,hooks_useLocale__WEBPACK_IMPORTED_MODULE_22__[\"default\"])();\n    const isProfileRoute = profileRoutes.find((item)=>pathname.includes(item));\n    const isDesktop = (0,_mui_material__WEBPACK_IMPORTED_MODULE_27__.useMediaQuery)(\"(min-width:1140px)\");\n    const dispatch = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_8__.useAppDispatch)();\n    const { isAuthenticated  } = (0,contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_11__.useAuth)();\n    const cart = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_8__.useAppSelector)(redux_slices_cart__WEBPACK_IMPORTED_MODULE_10__.selectCart);\n    const currency = (0,hooks_useRedux__WEBPACK_IMPORTED_MODULE_8__.useAppSelector)(redux_slices_currency__WEBPACK_IMPORTED_MODULE_12__.selectCurrency);\n    const { updateSettings  } = (0,contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_17__.useSettings)();\n    const { isDarkMode , setDirection  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(contexts_theme_theme_context__WEBPACK_IMPORTED_MODULE_19__.ThemeContext);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isShopDetailPage = router.pathname.startsWith(\"/shop/\");\n    (0,react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery)([\n        \"translation\",\n        locale\n    ], ()=>services_translations__WEBPACK_IMPORTED_MODULE_21__[\"default\"].getAll({\n            lang: locale\n        }), {\n        enabled: !!locale,\n        onSuccess: (data)=>{\n            addResourceBundle(locale, \"translation\", data.data);\n        }\n    });\n    (0,react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery)(\"currencies\", ()=>services_currency__WEBPACK_IMPORTED_MODULE_13__[\"default\"].getAll(), {\n        onSuccess: (data)=>{\n            // Prioritize BRL currency for Brazilian market\n            const brlCurrency = data.data.find((item)=>{\n                var ref, ref1;\n                return ((ref = item.title) === null || ref === void 0 ? void 0 : ref.toLowerCase().includes(\"real\")) || item.symbol === \"R$\" || ((ref1 = item.title) === null || ref1 === void 0 ? void 0 : ref1.toLowerCase().includes(\"brl\"));\n            });\n            const activeCurrency = brlCurrency || data.data.find((item)=>item.default);\n            const savedCurrency = data.data.find((item)=>{\n                return item.id === (currency === null || currency === void 0 ? void 0 : currency.id);\n            });\n            dispatch((0,redux_slices_currency__WEBPACK_IMPORTED_MODULE_12__.setDefaultCurrency)(activeCurrency));\n            if (savedCurrency) {\n                dispatch((0,redux_slices_currency__WEBPACK_IMPORTED_MODULE_12__.setCurrency)(savedCurrency));\n            } else {\n                dispatch((0,redux_slices_currency__WEBPACK_IMPORTED_MODULE_12__.setCurrency)(activeCurrency));\n            }\n        }\n    });\n    (0,react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery)(\"languages\", ()=>services_language__WEBPACK_IMPORTED_MODULE_14__[\"default\"].getAllActive(), {\n        onSuccess: (data)=>{\n            var ref;\n            const isRTL = !!((ref = data === null || data === void 0 ? void 0 : data.data.find((item)=>item.locale == locale)) === null || ref === void 0 ? void 0 : ref.backward);\n            setDirection(isRTL ? \"rtl\" : \"ltr\");\n        }\n    });\n    (0,react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery)(\"settings\", ()=>services_information__WEBPACK_IMPORTED_MODULE_16__[\"default\"].getSettings(), {\n        onSuccess: (data)=>{\n            const obj = (0,utils_createSettings__WEBPACK_IMPORTED_MODULE_23__[\"default\"])(data.data);\n            updateSettings({\n                payment_type: obj.payment_type,\n                instagram_url: obj.instagram,\n                facebook_url: obj.facebook,\n                twitter_url: obj.twitter,\n                referral_active: obj.referral_active,\n                otp_expire_time: obj.otp_expire_time,\n                customer_app_android: obj.customer_app_android,\n                customer_app_ios: obj.customer_app_ios,\n                delivery_app_android: obj.delivery_app_android,\n                delivery_app_ios: obj.delivery_app_ios,\n                vendor_app_android: obj.vendor_app_android,\n                vendor_app_ios: obj.vendor_app_ios,\n                group_order: obj.group_order,\n                footer_text: obj.footer_text,\n                ui_type: obj.ui_type,\n                address_text: obj.address,\n                phone: obj.phone,\n                email: obj.email,\n                reservation_time_durations: obj.reservation_time_durations,\n                reservation_before_time: obj.reservation_before_time,\n                min_reservation_time: obj.min_reservation_time,\n                active_parcel: obj.active_parcel,\n                before_order_phone_required: obj.before_order_phone_required,\n                reservation_enable_for_user: obj.reservation_enable_for_user\n            });\n        }\n    });\n    const { mutate: insertProducts  } = (0,react_query__WEBPACK_IMPORTED_MODULE_6__.useMutation)({\n        mutationFn: (data)=>services_cart__WEBPACK_IMPORTED_MODULE_7__[\"default\"].insert(data),\n        onSuccess: (data)=>{\n            dispatch((0,redux_slices_cart__WEBPACK_IMPORTED_MODULE_10__.clearCart)());\n            dispatch((0,redux_slices_userCart__WEBPACK_IMPORTED_MODULE_9__.updateUserCart)(data.data));\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isAuthenticated && !!cart.length) {\n            var ref;\n            let addons = [];\n            let products = [];\n            cart.forEach((item)=>{\n                products.push({\n                    stock_id: item.stock.id,\n                    quantity: item.quantity\n                });\n                item.addons.forEach((el)=>{\n                    addons.push({\n                        stock_id: el.stock.id,\n                        quantity: el.quantity,\n                        parent_id: item.stock.id\n                    });\n                });\n            });\n            const payload = {\n                shop_id: (ref = cart.find((item)=>!!item.shop_id)) === null || ref === void 0 ? void 0 : ref.shop_id,\n                currency_id: currency === null || currency === void 0 ? void 0 : currency.id,\n                rate: currency === null || currency === void 0 ? void 0 : currency.rate,\n                products: [\n                    ...products,\n                    ...addons\n                ]\n            };\n            insertProducts(payload);\n        }\n    }, [\n        cart,\n        currency,\n        isAuthenticated,\n        insertProducts\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        dayjs__WEBPACK_IMPORTED_MODULE_24___default().locale(locale);\n    }, [\n        locale\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(containers_errorBoundary_errorBoundary__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n        isDarkMode: isDarkMode,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"layout-container\",\n            children: [\n                isProfileRoute ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_profileHeader_profileHeader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\layout.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 11\n                }, this) : isDesktop ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_header_header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\layout.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mobileHeader_mobileHeader__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    isShopDetailPage: isShopDetailPage\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\layout.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, this),\n                children,\n                isAuthenticated && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PushNotification, {}, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\layout.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 29\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_footer_footer__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\layout.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\layout.tsx\",\n            lineNumber: 179,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\containers\\\\layout\\\\layout.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n_s(Layout, \"Xl8IyQpuzw8lJ8sqkpUgNP2Ouik=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        hooks_useLocale__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n        _mui_material__WEBPACK_IMPORTED_MODULE_27__.useMediaQuery,\n        hooks_useRedux__WEBPACK_IMPORTED_MODULE_8__.useAppDispatch,\n        contexts_auth_auth_context__WEBPACK_IMPORTED_MODULE_11__.useAuth,\n        hooks_useRedux__WEBPACK_IMPORTED_MODULE_8__.useAppSelector,\n        hooks_useRedux__WEBPACK_IMPORTED_MODULE_8__.useAppSelector,\n        contexts_settings_settings_context__WEBPACK_IMPORTED_MODULE_17__.useSettings,\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery,\n        react_query__WEBPACK_IMPORTED_MODULE_6__.useMutation\n    ];\n});\n_c1 = Layout;\nvar _c, _c1;\n$RefreshReg$(_c, \"PushNotification\");\n$RefreshReg$(_c1, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./containers/layout/layout.tsx\n"));

/***/ })

});