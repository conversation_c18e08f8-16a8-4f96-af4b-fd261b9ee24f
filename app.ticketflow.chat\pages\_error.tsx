import React from 'react';
import { NextPageContext } from 'next';
import { useTranslation } from 'react-i18next';

interface ErrorProps {
  statusCode?: number;
  hasGetInitialPropsRun?: boolean;
  err?: Error;
}

function Error({ statusCode, hasGetInitialPropsRun, err }: ErrorProps) {
  const { t } = useTranslation();

  // Handle specific error cases
  const getErrorMessage = () => {
    if (statusCode === 404) {
      return t('page_not_found') || 'Página não encontrada';
    }
    if (statusCode === 500) {
      return t('server_error') || 'Erro interno do servidor';
    }
    if (err?.message?.includes('Loading chunk')) {
      return 'Erro ao carregar recursos. Recarregue a página.';
    }
    return t('something_went_wrong') || 'Algo deu errado';
  };

  const handleReload = () => {
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      padding: '20px',
      textAlign: 'center',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      <h1 style={{ fontSize: '2rem', marginBottom: '1rem', color: '#333' }}>
        {statusCode ? `Erro ${statusCode}` : 'Erro do Cliente'}
      </h1>
      <p style={{ fontSize: '1.1rem', marginBottom: '2rem', color: '#666' }}>
        {getErrorMessage()}
      </p>
      <button
        onClick={handleReload}
        style={{
          padding: '12px 24px',
          fontSize: '1rem',
          backgroundColor: '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          cursor: 'pointer',
          transition: 'background-color 0.2s'
        }}
        onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#0056b3'}
        onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#007bff'}
      >
        Recarregar Página
      </button>
    </div>
  );
}

Error.getInitialProps = ({ res, err }: NextPageContext) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404;
  return { statusCode };
};

export default Error;
