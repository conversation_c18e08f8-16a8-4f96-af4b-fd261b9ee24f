"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./services/request.ts":
/*!*****************************!*\
  !*** ./services/request.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"./node_modules/axios/index.js\");\n/* harmony import */ var i18n__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18n */ \"./i18n.ts\");\n/* harmony import */ var constants_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! constants/constants */ \"./constants/constants.ts\");\n/* harmony import */ var utils_session__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! utils/session */ \"./utils/session.ts\");\n/* harmony import */ var components_alert_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! components/alert/toast */ \"./components/alert/toast.tsx\");\n//@ts-nocheck\n\n\n\n\n\nconst request = axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].create({\n    baseURL: constants_constants__WEBPACK_IMPORTED_MODULE_1__.API_URL\n});\nrequest.interceptors.request.use((config)=>{\n    const token = (0,utils_session__WEBPACK_IMPORTED_MODULE_2__.getCookieFromBrowser)(\"access_token\");\n    const locale = i18n__WEBPACK_IMPORTED_MODULE_0__[\"default\"].language;\n    if (token) {\n        config.headers.Authorization = token;\n    }\n    config.params = {\n        lang: locale,\n        ...config.params\n    };\n    return config;\n}, (error)=>errorHandler(error));\nfunction errorHandler(error) {\n    if (error === null || error === void 0 ? void 0 : error.response) {\n        var ref, ref1, ref2, ref3, ref4;\n        if ((error === null || error === void 0 ? void 0 : (ref = error.response) === null || ref === void 0 ? void 0 : ref.status) === 403) {} else if ((error === null || error === void 0 ? void 0 : (ref1 = error.response) === null || ref1 === void 0 ? void 0 : ref1.status) === 401) {\n            (0,components_alert_toast__WEBPACK_IMPORTED_MODULE_3__.error)(i18n__WEBPACK_IMPORTED_MODULE_0__[\"default\"].t(\"unauthorized\"), {\n                toastId: \"unauthorized\"\n            });\n            (0,utils_session__WEBPACK_IMPORTED_MODULE_2__.removeCookie)(\"user\");\n            (0,utils_session__WEBPACK_IMPORTED_MODULE_2__.removeCookie)(\"access_token\");\n            window.location.replace(\"/login\");\n        } else if ((error === null || error === void 0 ? void 0 : (ref2 = error.response) === null || ref2 === void 0 ? void 0 : ref2.status) === 404 && (error === null || error === void 0 ? void 0 : (ref3 = error.config) === null || ref3 === void 0 ? void 0 : (ref4 = ref3.url) === null || ref4 === void 0 ? void 0 : ref4.includes(\"/dashboard/user/cart\"))) {\n        // Don't log 404 errors for cart endpoints - these are expected when user is not authenticated\n        // The cart service will handle this gracefully with fallback\n        } else {\n            console.log(\"error => \", error);\n        }\n    } else {\n        console.log(\"error => \", error);\n    }\n    return Promise.reject(error.response);\n}\nrequest.interceptors.response.use((response)=>response.data, errorHandler);\n/* harmony default export */ __webpack_exports__[\"default\"] = (request);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./services/request.ts\n"));

/***/ })

});