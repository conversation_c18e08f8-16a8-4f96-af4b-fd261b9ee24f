{"__meta": {"id": "Xb1d2e0f5d17a282a3e2e5ff938654dda", "datetime": "2025-07-24 23:20:07", "utime": 1753410007.670549, "method": "POST", "uri": "/api/v1/dashboard/user/cart/insert-product?lang=pt-BR", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[23:20:07] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753410007.43595, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753410007.200707, "end": 1753410007.670588, "duration": 0.4698810577392578, "duration_str": "470ms", "measures": [{"label": "Booting", "start": 1753410007.200707, "relative_start": 0, "end": 1753410007.421104, "relative_end": 1753410007.421104, "duration": 0.2203969955444336, "duration_str": "220ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753410007.421113, "relative_start": 0.22040605545043945, "end": 1753410007.670593, "relative_end": 5.0067901611328125e-06, "duration": 0.2494800090789795, "duration_str": "249ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 45193280, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/v1/dashboard/user/cart/insert-product", "middleware": "api, block.ip, sanctum.check", "controller": "App\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController@insertProducts", "as": "user.", "namespace": null, "prefix": "api/v1/dashboard/user", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php&line=179\">\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php:179-191</a>"}, "queries": {"nb_statements": 67, "nb_failed_statements": 0, "accumulated_duration": 0.04538000000000003, "accumulated_duration_str": "45.38ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.02117, "duration_str": "21.17ms", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 0, "width_percent": 46.651}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 46.651, "width_percent": 0.749}, {"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 57}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 28}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CoreService.php:57", "connection": "foodyman", "start_percent": 47.4, "width_percent": 0.661}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 44}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CoreService.php", "line": 29}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\CoreService.php:44", "connection": "foodyman", "start_percent": 48.061, "width_percent": 0.683}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '25' limit 1", "type": "query", "params": [], "bindings": ["25"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 64}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php:64", "connection": "foodyman", "start_percent": 48.744, "width_percent": 1.432}, {"sql": "select * from `users` where `users`.`id` = 109 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["109"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 137}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "sanctum.check", "line": 17}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\vendor\\laravel\\sanctum\\src\\Guard.php:137", "connection": "foodyman", "start_percent": 50.176, "width_percent": 1.036}, {"sql": "select count(*) as aggregate from `shops` where `id` = 501 and `deleted_at` is null", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 51.212, "width_percent": 0.859}, {"sql": "select count(*) as aggregate from `currencies` where `id` = 1 and `deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 52.071, "width_percent": 0.749}, {"sql": "select count(*) as aggregate from `stocks` where `id` = 4 and `deleted_at` is null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 55}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 810}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 781}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 610}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 416}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php:55", "connection": "foodyman", "start_percent": 52.821, "width_percent": 0.639}, {"sql": "select * from `currencies` where `currencies`.`id` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 605}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:605", "connection": "foodyman", "start_percent": 53.46, "width_percent": 0.97}, {"sql": "select `id`, `shop_id`, `owner_id` from `carts` where `owner_id` = 109 limit 1", "type": "query", "params": [], "bindings": ["109"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 608}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:608", "connection": "foodyman", "start_percent": 54.429, "width_percent": 0.705}, {"sql": "select * from `carts` where (`owner_id` = 109 and `shop_id` = 501) limit 1", "type": "query", "params": [], "bindings": ["109", "501"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 55.134, "width_percent": 0.705}, {"sql": "select * from `shops` where `shops`.`id` in (501) and `shops`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 55.84, "width_percent": 0.727}, {"sql": "select * from `user_carts` where `user_carts`.`cart_id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 56.567, "width_percent": 0.617}, {"sql": "select * from `cart_details` where `cart_details`.`user_cart_id` in (12) and `parent_id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 57.184, "width_percent": 0.727}, {"sql": "select * from `stocks` where `stocks`.`id` in (3, 4) and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 57.911, "width_percent": 0.661}, {"sql": "select `id`, `status`, `shop_id`, `active`, `min_qty`, `max_qty`, `tax`, `img`, `interval` from `products` where `products`.`id` in (1, 2) and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 37, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 58.572, "width_percent": 0.683}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` in (1, 2) and `start` <= '2025-07-24 00:00:00' and `end` >= '2025-07-24 00:00:00' and `active` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-07-24 00:00:00", "2025-07-24 00:00:00", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 40, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 41, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00047999999999999996, "duration_str": "480μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 59.255, "width_percent": 1.058}, {"sql": "select * from `cart_details` where `cart_details`.`parent_id` in (13, 14)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 627}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00026000000000000003, "duration_str": "260μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:627", "connection": "foodyman", "start_percent": 60.313, "width_percent": 0.573}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (109) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 647}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php:188", "connection": "foodyman", "start_percent": 60.886, "width_percent": 0.727}, {"sql": "select * from `user_carts` where `user_carts`.`cart_id` = 12 and `user_carts`.`cart_id` is not null and (`user_id` = 109 and `cart_id` = 12) limit 1", "type": "query", "params": [], "bindings": ["12", "109", "12"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 651}, {"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:651", "connection": "foodyman", "start_percent": 61.613, "width_percent": 0.903}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 10, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 11, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 12, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 721}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 19, "namespace": "middleware", "name": "sanctum.check", "line": 18}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 21, "namespace": "middleware", "name": "block.ip", "line": 24}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\TrustLicence.php", "line": 65}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 27, "namespace": "middleware", "name": "throttle", "line": 127}, {"index": 28, "namespace": "middleware", "name": "throttle", "line": 58}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 103}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 719}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 698}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 662}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 651}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 167}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31}, {"index": 41, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 40}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php", "line": 27}, {"index": 46, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 47, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 86}, {"index": 48, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 49, "namespace": null, "name": "\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php", "line": 52}], "duration": 0, "duration_str": "", "stmt_id": "\\app\\Services\\CartService\\CartService.php:673", "connection": "foodyman", "start_percent": 62.517, "width_percent": 0}, {"sql": "delete from `cart_details` where `user_cart_id` = 12 and `bonus` = 1", "type": "query", "params": [], "bindings": ["12", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 678}, {"index": 14, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:678", "connection": "foodyman", "start_percent": 62.517, "width_percent": 0.837}, {"sql": "select * from `stocks` where exists (select * from `products` where `stocks`.`countable_id` = `products`.`id` and `status` = 'published' and `active` = 1 and `shop_id` = 501 and `products`.`deleted_at` is null) and `stocks`.`id` = 4 and `stocks`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["published", "1", "501", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 708}, {"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:708", "connection": "foodyman", "start_percent": 63.354, "width_percent": 0.881}, {"sql": "select `id`, `status`, `active`, `shop_id`, `min_qty`, `max_qty`, `tax`, `img`, `interval` from `products` where `products`.`id` in (1) and `status` = 'published' and `active` = 1 and `shop_id` = 501 and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["published", "1", "501"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 708}, {"index": 25, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 26, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 27, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 28, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:708", "connection": "foodyman", "start_percent": 64.235, "width_percent": 0.705}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` in (1) and `start` <= '2025-07-24 00:00:00' and `end` >= '2025-07-24 00:00:00' and `active` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-07-24 00:00:00", "2025-07-24 00:00:00", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 708}, {"index": 29, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 30, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 31, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:708", "connection": "foodyman", "start_percent": 64.941, "width_percent": 0.926}, {"sql": "select * from `units` where 0 = 1 and `units`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 708}, {"index": 30, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 31, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 32, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 33, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:708", "connection": "foodyman", "start_percent": 65.866, "width_percent": 0.595}, {"sql": "select * from `cart_details` where not exists (select * from `cart_details` as `laravel_reserved_0` where `cart_details`.`id` = `laravel_reserved_0`.`parent_id`) and (`user_cart_id` = 12 and `stock_id` = 4 and `parent_id` is null) limit 1", "type": "query", "params": [], "bindings": ["12", "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 760}, {"index": 19, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:760", "connection": "foodyman", "start_percent": 66.461, "width_percent": 0.926}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'cart_details'", "type": "query", "params": [], "bindings": ["foodyman", "cart_details"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 764}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 19, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:764", "connection": "foodyman", "start_percent": 67.387, "width_percent": 1.719}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 673}, {"index": 9, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 658}, {"index": 10, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 11, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 721}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 18, "namespace": "middleware", "name": "sanctum.check", "line": 18}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 20, "namespace": "middleware", "name": "block.ip", "line": 24}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Middleware\\TrustLicence.php", "line": 65}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 26, "namespace": "middleware", "name": "throttle", "line": 127}, {"index": 27, "namespace": "middleware", "name": "throttle", "line": 58}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 103}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 719}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 698}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 662}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 651}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 167}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 128}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 31}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 41, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php", "line": 21}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php", "line": 40}, {"index": 43, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php", "line": 27}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 46, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 86}, {"index": 47, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 48, "namespace": null, "name": "\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php", "line": 52}, {"index": 49, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0, "duration_str": "", "stmt_id": "\\app\\Services\\CartService\\CartService.php:673", "connection": "foodyman", "start_percent": 69.105, "width_percent": 0}, {"sql": "select `id`, `total_price`, `shop_id` from `carts` where `carts`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1161}, {"index": 17, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1161", "connection": "foodyman", "start_percent": 69.105, "width_percent": 0.639}, {"sql": "select * from `user_carts` where `user_carts`.`cart_id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1161}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1161", "connection": "foodyman", "start_percent": 69.744, "width_percent": 0.793}, {"sql": "select `id`, `user_cart_id`, `stock_id`, `price`, `discount`, `quantity`, `bonus_type` from `cart_details` where `cart_details`.`user_cart_id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1161}, {"index": 27, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 28, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 29, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1161", "connection": "foodyman", "start_percent": 70.538, "width_percent": 0.617}, {"sql": "select * from `stocks` where `stocks`.`id` in (3, 4) and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1161}, {"index": 32, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 33, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 34, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1161", "connection": "foodyman", "start_percent": 71.155, "width_percent": 0.639}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_id` in (3, 4) and `bonuses`.`bonusable_type` = 'App\\Models\\Stock' and `expired_at` > '2025-07-24 23:20:07' and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Stock", "2025-07-24 23:20:07"], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1161}, {"index": 37, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 38, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 39, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1161", "connection": "foodyman", "start_percent": 71.794, "width_percent": 0.683}, {"sql": "select * from `shops` where `shops`.`id` in (501) and `shops`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1161}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1161", "connection": "foodyman", "start_percent": 72.477, "width_percent": 0.705}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_id` in (501) and `bonuses`.`bonusable_type` = 'App\\Models\\Shop' and `expired_at` > '2025-07-24 23:20:07' and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "2025-07-24 23:20:07"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1161}, {"index": 27, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 28, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 29, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1161", "connection": "foodyman", "start_percent": 73.182, "width_percent": 0.661}, {"sql": "delete from `cart_details` where `user_cart_id` in (12) and `bonus` = 1", "type": "query", "params": [], "bindings": ["12", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1215}, {"index": 11, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 12, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 13, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1215", "connection": "foodyman", "start_percent": 73.843, "width_percent": 0.926}, {"sql": "select * from `products` where `products`.`id` = 2 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1318}, {"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1227}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 24, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1318", "connection": "foodyman", "start_percent": 74.769, "width_percent": 0.771}, {"sql": "select * from `products` where `products`.`id` = 1 and `products`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1318}, {"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1227}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 24, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}], "duration": 0.00051, "duration_str": "510μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1318", "connection": "foodyman", "start_percent": 75.54, "width_percent": 1.124}, {"sql": "select * from `bonuses` where `type` = 'count' and `bonusable_type` = 'App\\Models\\Stock' and `bonusable_id` = 3 and `value` <= 2 and `expired_at` >= '2025-07-24 23:20:07' and `bonuses`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["count", "App\\Models\\Stock", "3", "2", "2025-07-24 23:20:07"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1253}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 17, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1253", "connection": "foodyman", "start_percent": 76.664, "width_percent": 0.859}, {"sql": "select * from `bonuses` where `type` = 'count' and `bonusable_type` = 'App\\Models\\Stock' and `bonusable_id` = 4 and `value` <= 1 and `expired_at` >= '2025-07-24 23:20:07' and `bonuses`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["count", "App\\Models\\Stock", "4", "1", "2025-07-24 23:20:07"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1253}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 17, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1253", "connection": "foodyman", "start_percent": 77.523, "width_percent": 0.793}, {"sql": "select * from `receipts` where exists (select * from `stocks` inner join `receipt_stocks` on `stocks`.`id` = `receipt_stocks`.`stock_id` where `receipts`.`id` = `receipt_stocks`.`receipt_id` and `stocks`.`deleted_at` is null) and `shop_id` = 501 and `receipts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1350}, {"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1264}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 17, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1350", "connection": "foodyman", "start_percent": 78.316, "width_percent": 0.903}, {"sql": "select * from `carts` where `id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1266}, {"index": 17, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 19, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1266", "connection": "foodyman", "start_percent": 79.22, "width_percent": 0.749}, {"sql": "select * from `user_carts` where `user_carts`.`cart_id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1266}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 24, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1266", "connection": "foodyman", "start_percent": 79.969, "width_percent": 0.661}, {"sql": "select * from `shops` where `shops`.`id` = 501 and `shops`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["501"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1277}, {"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00035, "duration_str": "350μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1277", "connection": "foodyman", "start_percent": 80.63, "width_percent": 0.771}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_type` = 'App\\Models\\Shop' and `bonuses`.`bonusable_id` = 501 and `bonuses`.`bonusable_id` is not null and `bonuses`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "501"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1277}, {"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1277", "connection": "foodyman", "start_percent": 81.401, "width_percent": 0.948}, {"sql": "select column_name as `column_name` from information_schema.columns where table_schema = 'foodyman' and table_name = 'carts'", "type": "query", "params": [], "bindings": ["foodyman", "carts"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1304}, {"index": 15, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1171}, {"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 17, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1304", "connection": "foodyman", "start_percent": 82.349, "width_percent": 1.587}, {"sql": "select * from `carts` where `carts`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 17, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 83.936, "width_percent": 0.661}, {"sql": "select * from `shops` where `shops`.`id` in (501) and `shops`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 84.597, "width_percent": 0.705}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_id` in (501) and `bonuses`.`bonusable_type` = 'App\\Models\\Shop' and `expired_at` > '2025-07-24 23:20:07' and `status` = 1 and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "2025-07-24 23:20:07", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 27, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 28, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 29, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00033, "duration_str": "330μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 85.302, "width_percent": 0.727}, {"sql": "select * from `user_carts` where `user_carts`.`cart_id` in (12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 22, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 23, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 86.029, "width_percent": 0.661}, {"sql": "select * from `cart_details` where `cart_details`.`user_cart_id` in (12) and `parent_id` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 27, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 28, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 29, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 86.69, "width_percent": 0.661}, {"sql": "select * from `stocks` where `stocks`.`id` in (3, 4) and `stocks`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 32, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 33, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 34, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 87.351, "width_percent": 0.705}, {"sql": "select * from `bonuses` where `bonuses`.`bonusable_id` in (3, 4) and `bonuses`.`bonusable_type` = 'App\\Models\\Stock' and `expired_at` > '2025-07-24 23:20:07' and `status` = 1 and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Stock", "2025-07-24 23:20:07", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 37, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 38, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 39, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00041, "duration_str": "410μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 88.056, "width_percent": 0.903}, {"sql": "select * from `products` where `products`.`id` in (1, 2) and `products`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 36, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 37, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 38, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 39, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 88.96, "width_percent": 0.815}, {"sql": "select * from `units` where `units`.`id` in (1) and `units`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 41, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 42, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 43, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 44, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00028000000000000003, "duration_str": "280μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 89.775, "width_percent": 0.617}, {"sql": "select * from `unit_translations` where `unit_translations`.`unit_id` in (1) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `unit_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 46, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 47, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 48, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 49, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 90.392, "width_percent": 0.661}, {"sql": "select * from `product_translations` where `product_translations`.`product_id` in (1, 2) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `product_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 41, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 42, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 43, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 44, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 45, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 91.053, "width_percent": 0.705}, {"sql": "select `extra_values`.*, `stock_extras`.`stock_id` as `pivot_stock_id`, `stock_extras`.`extra_value_id` as `pivot_extra_value_id` from `extra_values` inner join `stock_extras` on `extra_values`.`id` = `stock_extras`.`extra_value_id` where `stock_extras`.`stock_id` in (3, 4) and `extra_values`.`deleted_at` is null order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 35, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 36, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 37, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 38, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 91.758, "width_percent": 0.749}, {"sql": "select * from `cart_details` where `cart_details`.`parent_id` in (13, 14)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 31, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 1193}, {"index": 32, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 660}, {"index": 33, "namespace": null, "name": "\\app\\Services\\CartService\\CartService.php", "line": 632}, {"index": 34, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Dashboard\\User\\CartController.php", "line": 181}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Services\\CartService\\CartService.php:1193", "connection": "foodyman", "start_percent": 92.508, "width_percent": 0.661}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 501 and `orders`.`shop_id` is not null and (`shop_id` = 501 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["501", "501", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00054, "duration_str": "540μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 93.169, "width_percent": 1.19}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 501 and `orders`.`shop_id` is not null and (`shop_id` = 501 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["501", "501", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00044, "duration_str": "440μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 94.359, "width_percent": 0.97}, {"sql": "select * from `user_carts` where `user_carts`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\CartDetail.php", "line": 60}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Resources\\Cart\\CartDetailResource.php", "line": 25}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 59}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Models\\CartDetail.php:60", "connection": "foodyman", "start_percent": 95.328, "width_percent": 0.705}, {"sql": "select * from `carts` where `carts`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\CartDetail.php", "line": 60}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Resources\\Cart\\CartDetailResource.php", "line": 25}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 59}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Models\\CartDetail.php:60", "connection": "foodyman", "start_percent": 96.033, "width_percent": 0.661}, {"sql": "select * from `user_carts` where `user_carts`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\CartDetail.php", "line": 60}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Resources\\Cart\\CartDetailResource.php", "line": 25}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 59}], "duration": 0.00029, "duration_str": "290μs", "stmt_id": "\\app\\Models\\CartDetail.php:60", "connection": "foodyman", "start_percent": 96.695, "width_percent": 0.639}, {"sql": "select * from `carts` where `carts`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": ["12"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\CartDetail.php", "line": 60}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Resources\\Cart\\CartDetailResource.php", "line": 25}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 60}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php", "line": 59}], "duration": 0.00035999999999999997, "duration_str": "360μs", "stmt_id": "\\app\\Models\\CartDetail.php:60", "connection": "foodyman", "start_percent": 97.334, "width_percent": 0.793}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 2 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 183}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\StockResource.php", "line": 32}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.0004, "duration_str": "400μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 98.127, "width_percent": 0.881}, {"sql": "select `discounts`.*, `product_discounts`.`product_id` as `pivot_product_id`, `product_discounts`.`discount_id` as `pivot_discount_id` from `discounts` inner join `product_discounts` on `discounts`.`id` = `product_discounts`.`discount_id` where `product_discounts`.`product_id` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 159}, {"index": 25, "namespace": null, "name": "\\app\\Models\\Stock.php", "line": 183}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 32, "namespace": null, "name": "\\app\\Http\\Resources\\StockResource.php", "line": 32}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}], "duration": 0.00045, "duration_str": "450μs", "stmt_id": "\\app\\Models\\Stock.php:159", "connection": "foodyman", "start_percent": 99.008, "width_percent": 0.992}]}, "models": {"data": {"App\\Models\\ProductTranslation": 2, "App\\Models\\Unit": 1, "Spatie\\Permission\\Models\\Role": 1, "App\\Models\\Product": 7, "App\\Models\\Stock": 7, "App\\Models\\CartDetail": 7, "App\\Models\\UserCart": 7, "App\\Models\\Shop": 4, "App\\Models\\Cart": 7, "App\\Models\\User": 1, "Laravel\\Sanctum\\PersonalAccessToken": 1, "App\\Models\\Currency": 3, "App\\Models\\Language": 2}, "count": 50}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f78c210-b80a-4d54-96d3-71e3d2a7ee5d\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/dashboard/user/cart/insert-product", "status_code": "<pre class=sf-dump id=sf-dump-2091339056 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2091339056\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2127635112 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2127635112\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1510611139 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>shop_id</span>\" => <span class=sf-dump-num>501</span>\n  \"<span class=sf-dump-key>currency_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>rate</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>products</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>stock_id</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>receipt_discount</span>\" => <span class=sf-dump-num>0</span>\n  \"<span class=sf-dump-key>receipt_count</span>\" => <span class=sf-dump-num>0</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1510611139\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1246353886 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">81</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">Bearer 25|BDXbYGnUezbkTljZPgqRpjj1nzTxlsWEC4Q5o4YD</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1246353886\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1395134988 data-indent-pad=\"  \"><span class=sf-dump-note>array:39</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">61203</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"53 characters\">/api/v1/dashboard/user/cart/insert-product?lang=pt-BR</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"42 characters\">/api/v1/dashboard/user/cart/insert-product</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"52 characters\">/index.php/api/v1/dashboard/user/cart/insert-product</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lang=pt-BR</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">81</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"2 characters\">81</span>\"\n  \"<span class=sf-dump-key>HTTP_PRAGMA</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_AUTHORIZATION</span>\" => \"<span class=sf-dump-str title=\"50 characters\">Bearer 25|BDXbYGnUezbkTljZPgqRpjj1nzTxlsWEC4Q5o4YD</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753410007.2007</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753410007</span>\n  \"<span class=sf-dump-key>argv</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">lang=pt-BR</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1395134988\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1426925304 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1426925304\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-583638602 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 02:20:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4996</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-583638602\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1242852230 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1242852230\", {\"maxDepth\":0})</script>\n"}}