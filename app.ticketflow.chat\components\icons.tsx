import {
  <PERSON>AN<PERSON>_LOGO,
  BRAND_LOGO_DARK,
  BRAND_LOGO_ROUNDED,
} from "constants/config";
import { useTranslation } from "react-i18next";

/* eslint-disable @next/next/no-img-element */
export const BrandLogo = () => {
  const { t } = useTranslation();
  return <img src={BRAND_LOGO} width="129" height="28" alt={t("brand.logo")} />;
};

export const BrandLogoDark = () => {
  const { t } = useTranslation();
  return <img src={BRAND_LOGO_DARK} width="129" height="28" alt={t("brand.logo.dark")} />;
};

export const DoubleCheckIcon = () => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="15" cy="15" r="15" fill="#83EA00" />
    <g clipPath="url(#clip0_109_101)">
      <path
        d="M14.668 16.4666L15.8447 17.6433L22.8997 10.5883L24.078 11.7666L15.8447 19.9999L10.5413 14.6966L11.7197 13.5183L13.4905 15.2891L14.668 16.4658V16.4666ZM14.6697 14.1099L18.7963 9.98242L19.9713 11.1574L15.8447 15.2849L14.6697 14.1099ZM12.3138 18.8224L11.1363 19.9999L5.83301 14.6966L7.01134 13.5183L8.18884 14.6958L8.18801 14.6966L12.3138 18.8224Z"
        fill="black"
      />
    </g>
    <defs>
      <clipPath id="clip0_109_101">
        <rect width="20" height="20" fill="white" transform="translate(5 5)" />
      </clipPath>
    </defs>
  </svg>
);

export const BrandLogoRounded = () => (
  <img
    src={BRAND_LOGO_ROUNDED}
    width="60"
    height="60"
    alt="Brand logo rounded"
  />
);

export const EveryDay = () => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.0046 11.9192C12.5233 11.9192 13.7546 13.1505 13.7546 14.6692C13.7546 15.4477 13.4312 16.1505 12.9114 16.6508L11.1605 18.3355L13.7546 18.3359V20.1692H8.25456L8.25373 18.5885L11.6402 15.3298C11.8135 15.1628 11.9212 14.9286 11.9212 14.6692C11.9212 14.163 11.5109 13.7526 11.0046 13.7526C10.4983 13.7526 10.0879 14.163 10.0879 14.6692H8.25456C8.25456 13.1505 9.48575 11.9192 11.0046 11.9192ZM16.5046 11.9192V15.5859H18.3379V11.9192H20.1712V20.1692H18.3379V17.4192H14.6712V11.9192H16.5046ZM3.67122 11.0026C3.67122 13.3186 4.74486 15.3839 6.42158 16.7278L6.42124 18.9429C3.68131 17.358 1.83789 14.3955 1.83789 11.0026H3.67122ZM11.0046 1.83594C15.7578 1.83594 19.666 5.45367 20.126 10.0858L18.2812 10.086C17.8301 6.46851 14.7442 3.66927 11.0046 3.66927C8.48393 3.66927 6.26032 4.941 4.94037 6.87782L7.33789 6.8776V8.71094H1.83789V3.21094H3.67122L3.67112 5.50185C5.34354 3.27579 8.0059 1.83594 11.0046 1.83594Z"
      fill="white"
    />
  </svg>
);

export const Gift = () => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.2962 1.83594C15.0682 1.83594 16.5046 3.27235 16.5046 5.04427C16.5046 5.53671 16.3937 6.00324 16.1954 6.42022L19.2546 6.41927C19.7609 6.41927 20.1712 6.82967 20.1712 7.33594V11.0026C20.1712 11.5089 19.7609 11.9192 19.2546 11.9192H18.3379V19.2526C18.3379 19.7589 17.9275 20.1692 17.4212 20.1692H4.58789C4.08163 20.1692 3.67122 19.7589 3.67122 19.2526V11.9192H2.75456C2.2483 11.9192 1.83789 11.5089 1.83789 11.0026V7.33594C1.83789 6.82967 2.2483 6.41927 2.75456 6.41927L5.81376 6.42022C5.6155 6.00324 5.50456 5.53671 5.50456 5.04427C5.50456 3.27235 6.94098 1.83594 8.71289 1.83594C9.61124 1.83594 10.4233 2.20513 11.0057 2.80007C11.5858 2.20513 12.3979 1.83594 13.2962 1.83594ZM16.5046 11.9192H5.50456V18.3359H16.5046V11.9192ZM18.3379 8.2526H3.67122V10.0859H18.3379V8.2526ZM8.71289 3.66927C7.95351 3.66927 7.33789 4.28488 7.33789 5.04427C7.33789 5.759 7.88321 6.34635 8.58047 6.41297L8.71289 6.41927H10.0879V5.04427C10.0879 4.32955 9.54258 3.74219 8.84531 3.67557L8.71289 3.66927ZM13.2962 3.66927L13.1638 3.67557C12.5101 3.73803 11.99 4.25817 11.9275 4.91185L11.9212 5.04427V6.41927H13.2962L13.4286 6.41297C14.1259 6.34635 14.6712 5.759 14.6712 5.04427C14.6712 4.32955 14.1259 3.74219 13.4286 3.67557L13.2962 3.66927Z"
      fill="white"
    />
  </svg>
);

export const Flash = () => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M11.917 9.16675H18.3337L10.0837 21.0834V12.8334H3.66699L11.917 0.916748V9.16675Z"
      fill="white"
    />
  </svg>
);

export const PickupFromIcon = () => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.04208 18.3338C5.80147 18.3338 6.41708 18.9493 6.41708 19.7088C6.41708 20.4682 5.80147 21.0838 5.04208 21.0838C4.28269 21.0838 3.66708 20.4682 3.66708 19.7088C3.66708 18.9493 4.28269 18.3338 5.04208 18.3338ZM16.9588 18.3338C17.7182 18.3338 18.3338 18.9493 18.3338 19.7088C18.3338 20.4682 17.7182 21.0838 16.9588 21.0838C16.1993 21.0838 15.5838 20.4682 15.5838 19.7088C15.5838 18.9493 16.1993 18.3338 16.9588 18.3338ZM1.99103 1.61133L5.49983 5.12033V15.5832L18.3338 15.5838V17.4171H4.58375C4.07749 17.4171 3.66708 17.0066 3.66708 16.5005L3.66649 5.87933L0.69466 2.9077L1.99103 1.61133ZM14.6671 2.75041C15.1733 2.75041 15.5838 3.16082 15.5838 3.66708L15.5831 5.49983L18.3275 5.50041C18.8372 5.50041 19.2505 5.91811 19.2505 6.41286V13.7547C19.2505 14.2586 18.838 14.6671 18.3275 14.6671H7.34002C6.83029 14.6671 6.41708 14.2494 6.41708 13.7547V6.41286C6.41708 5.90894 6.82953 5.50041 7.34002 5.50041L10.0831 5.49983L10.0838 3.66708C10.0838 3.16082 10.4942 2.75041 11.0005 2.75041H14.6671ZM9.16649 7.33316L8.25041 7.33375V12.8338L9.16649 12.8332V7.33316ZM14.6665 7.33316H10.9998V12.8332H14.6665V7.33316ZM17.4171 7.33375L16.4998 7.33316V12.8332L17.4171 12.8338V7.33375ZM13.7505 4.58375H11.9171V5.50041H13.7505V4.58375Z"
      fill="black"
    />
  </svg>
);

export const DeliveryIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
  >
    <path
      d="M3.07795 10.6919H0.899951C0.647951 10.6919 0.449951 10.8899 0.449951 11.1419C0.449951 11.3939 0.647951 11.5919 0.899951 11.5919H3.07795C3.32995 11.5919 3.52795 11.3939 3.52795 11.1419C3.52795 10.8899 3.32995 10.6919 3.07795 10.6919Z"
      fill="black"
    />
    <path
      d="M3.07806 9.01782H1.45806C1.20606 9.01782 1.00806 9.21582 1.00806 9.46782C1.00806 9.71982 1.20606 9.91782 1.45806 9.91782H3.09606C3.34806 9.91782 3.54606 9.71982 3.54606 9.46782C3.52806 9.21582 3.33006 9.01782 3.07806 9.01782Z"
      fill="black"
    />
    <path
      d="M3.07785 7.36206H1.99785C1.74585 7.36206 1.54785 7.56006 1.54785 7.81206C1.54785 8.06406 1.74585 8.26206 1.99785 8.26206H3.07785C3.32985 8.26206 3.52785 8.06406 3.52785 7.81206C3.52785 7.56006 3.32985 7.36206 3.07785 7.36206Z"
      fill="black"
    />
    <path
      d="M16.542 9.01792L15.462 8.83792L14.814 6.89392C14.652 6.40792 14.184 6.06592 13.68 6.06592H9.37798C9.37798 6.37192 9.32398 6.67792 9.23398 6.96592H11.628V9.23392C11.628 9.48592 11.826 9.68392 12.078 9.68392H15.084L16.398 9.89992C16.542 9.91792 16.65 10.0439 16.65 10.1879V11.6279C16.65 11.7899 16.524 11.9339 16.344 11.9339H15.912C15.642 11.3939 15.102 11.0339 14.454 11.0339C13.824 11.0339 13.266 11.4119 12.996 11.9339H8.98198C8.71198 11.3939 8.17198 11.0339 7.52398 11.0339C6.89398 11.0339 6.33598 11.4119 6.06598 11.9339H5.63398C5.47198 11.9339 5.32798 11.8079 5.32798 11.6279V9.01792C5.00398 8.92792 4.69798 8.76592 4.42798 8.56792V11.6459C4.42798 12.3119 4.96798 12.8519 5.63398 12.8519H5.90398C5.99398 13.6619 6.67798 14.2919 7.52398 14.2919C8.36998 14.2919 9.05398 13.6619 9.14398 12.8519H12.834C12.924 13.6619 13.608 14.2919 14.454 14.2919C15.282 14.2919 15.984 13.6619 16.074 12.8519H16.362C17.028 12.8519 17.568 12.3119 17.568 11.6459V10.2059C17.55 9.61192 17.118 9.10792 16.542 9.01792ZM12.528 8.78392V6.98392H13.68C13.806 6.98392 13.932 7.07392 13.968 7.18192L14.508 8.78392H12.528ZM7.52398 13.3919C7.12798 13.3919 6.80398 13.0679 6.80398 12.6719C6.80398 12.2759 7.12798 11.9519 7.52398 11.9519C7.91998 11.9519 8.24398 12.2759 8.24398 12.6719C8.24398 13.0679 7.93798 13.3919 7.52398 13.3919ZM14.454 13.3919C14.058 13.3919 13.734 13.0679 13.734 12.6719C13.734 12.2759 14.058 11.9519 14.454 11.9519C14.85 11.9519 15.174 12.2759 15.174 12.6719C15.174 13.0679 14.85 13.3919 14.454 13.3919Z"
      fill="black"
    />
    <path
      d="M6.24607 8.33394C7.52407 8.33394 8.56807 7.28994 8.56807 6.01194C8.56807 4.73394 7.52407 3.68994 6.24607 3.68994C4.96807 3.68994 3.92407 4.73394 3.92407 6.01194C3.92407 7.28994 4.96807 8.33394 6.24607 8.33394ZM5.88607 4.80594C5.88607 4.60794 6.04807 4.44594 6.24607 4.44594C6.44407 4.44594 6.60607 4.60794 6.60607 4.80594V5.65194H7.14607C7.34407 5.65194 7.50607 5.81394 7.50607 6.01194C7.50607 6.20994 7.34407 6.37194 7.14607 6.37194H6.26407C6.06607 6.37194 5.90407 6.20994 5.90407 6.01194V4.80594H5.88607Z"
      fill="black"
    />
  </svg>
);

export const BoxLineIcon = () => (
  <svg
    width="22"
    height="22"
    viewBox="0 0 22 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.5416 0.916626L19.25 5.95829V16.0416L10.5416 21.0833L1.83331 16.0416V5.95829L10.5416 0.916626ZM4.5777 6.48785L10.5417 9.94066L16.5057 6.48789L10.5416 3.03504L4.5777 6.48785ZM3.66665 8.07881V14.9846L9.62507 18.4342V11.5284L3.66665 8.07881ZM11.4584 18.4341L17.4166 14.9846V8.07888L11.4584 11.5284V18.4341Z"
      fill="black"
    />
  </svg>
);

export const VerifiedIcon = () => (
  <svg
    viewBox="0 0 24 24"
    aria-hidden="true"
    data-testid="verificationBadge"
    version="1.1"
    id="svg965"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
  >
    {/* <defs id="defs969" /> */}
    <g id="g963">
      <path
        d="M22.25 12c0-1.43-.88-2.67-2.19-3.34.46-1.39.2-2.9-.81-3.91s-2.52-1.27-3.91-.81c-.66-1.31-1.91-2.19-3.34-2.19s-2.67.88-3.33 2.19c-1.4-.46-2.91-.2-3.92.81s-1.26 2.52-.8 3.91c-1.31.67-2.2 1.91-2.2 3.34s.89 2.67 2.2 3.34c-.46 1.39-.21 2.9.8 3.91s2.52 1.26 3.91.81c.67 1.31 1.91 2.19 3.34 2.19s2.68-.88 3.34-2.19c1.39.45 2.9.2 3.91-.81s1.27-2.52.81-3.91c1.31-.67 2.19-1.91 2.19-3.34zm-11.71 4.2L6.8 12.46l1.41-1.42 2.26 2.26 4.8-5.23 1.47 1.36-6.2 6.77z"
        id="path961"
        fill="#42A5F5"
      />
    </g>
  </svg>
);

// Payment Method Icons
export const CashDeliveryIcon = ({ size = 24 }: { size?: number }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g transform="scale(0.5)">
      <path d="M5.344 3.656c1.16.316 1.16.316 1.628.47l.32.105.338.112.362.119.966.318.809.266q2.63.865 5.256 1.743 1.421.477 2.847.942l.21.068q.619.201 1.239.398c.816.26 1.624.53 2.423.839 3.121 1.205 5.871 1.615 9.204 1.56.545-.007 1.089-.005 1.634-.004 1.574-.002 3.148-.018 4.721-.033L48 10.5v27l-3.094.094-2.25 6.75c-.592-.162-1.168-.319-1.748-.511l-.197-.065-.643-.213-.469-.155-1.276-.422-1.353-.447-2.338-.773-3.622-1.196-6.632-2.189-.227-.075-.223-.074-.234-.075a10 10 0 0 1-.551-.209c-1.091-.424-2.135-.414-3.294-.409l-.662-.003q-.881-.003-1.763-.002-.829 0-1.658-.004-2.42-.006-4.839-.006L0 37.5v-27l3.094-.094zm1.875 3.656L6.188 10.5h10.5a2.4 2.4 0 0 0-.66-.324l-.232-.077-.254-.083-.268-.089-.88-.289-.61-.201-1.278-.42-1.642-.54-1.865-.614-.844-.277-.255-.084c-.335-.131-.335-.131-.681-.189m-4.406 6.094v2.625c.554-.111.863-.331 1.313-.656l.293-.205c.526-.453.859-1.133 1.112-1.765zm5.719 0-.563 1.594c-.776 1.622-2.059 2.997-3.767 3.639a20 20 0 0 1-1.005.307c-.277.056-.277.056-.385.179a5 5 0 0 0-.011.381v.249l.001.275v.288l.002.948v.655l.002 1.729.002 1.763.005 3.461.173.057.228.076.225.075c.904.307 1.721.693 2.467 1.292l.236.186c1.294 1.088 1.862 2.451 2.389 4.033h30.938L40.032 33c.776-1.622 2.059-2.997 3.767-3.639a20 20 0 0 1 1.005-.307c.277-.056.277-.056.385-.179a5 5 0 0 0 .011-.381v-.249l-.001-.275v-.288l-.002-.948v-.655l-.002-1.729-.002-1.763-.005-3.461-.173-.057-.228-.076-.226-.075c-2.089-.706-3.524-1.939-4.53-3.918l-.563-1.594zm33.938 0c.606 1.212 1.166 2.084 2.438 2.625h.281v-2.625zM2.813 31.969v2.625h2.719c-.606-1.212-1.166-2.084-2.438-2.625zm40.656.953c-.444.49-.756 1.062-1 1.672h2.719v-2.625c-.617 0-1.269.55-1.719.953M31.313 37.5c.334.223.678.331 1.056.454l.239.079.785.258.547.18 1.148.377 1.466.482 2.43.798.222.073c.523.171 1.047.329 1.575.487l1.031-3.188z"/>
      <path d="M28.939 18.533c1.529 1.461 2.342 3.311 2.396 5.42-.019 1.972-.744 3.808-2.112 5.241a7.6 7.6 0 0 1-3.91 2.025l-.316.065c-2.085.18-4.026-.297-5.693-1.605-1.408-1.198-2.426-2.95-2.603-4.809-.12-2.1.354-4.074 1.746-5.711 2.724-3.05 7.424-3.292 10.494-.624m-8.287 2.569c-.767.969-1.175 2.128-1.06 3.365.202 1.267.769 2.295 1.781 3.094 1.041.671 2.135.997 3.367.8 1.186-.259 2.251-.853 2.922-1.895.656-1.126.917-2.211.637-3.493-.317-1.142-.958-2.128-1.998-2.735-1.959-1.059-4.09-.784-5.65.862m14.972 1.492h5.063v2.813h-5.063zm-28.219 0h5.063v2.813H7.406z"/>
    </g>
  </svg>
);

export const PixDeliveryIcon = ({ size = 24 }: { size?: number }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g transform="scale(1.5)">
      <path d="M11.917 11.71a2.046 2.046 0 0 1-1.454-.602l-2.1-2.1a.4.4 0 0 0-.551 0l-2.108 2.108a2.044 2.044 0 0 1-1.454.602h-.414l2.66 2.66c.83.83 2.177.83 3.007 0l2.667-2.668h-.253zM4.25 4.282c.55 0 1.066.214 1.454.602l2.108 2.108a.39.39 0 0 0 .552 0l2.1-2.1a2.044 2.044 0 0 1 1.453-.602h.253L9.503 1.623a2.127 2.127 0 0 0-3.007 0l-2.66 2.66h.414z"/>
      <path d="m14.377 6.496-1.612-1.612a.307.307 0 0 1-.114.023h-.733c-.379 0-.75.154-1.017.422l-2.1 2.1a1.005 1.005 0 0 1-1.425 0L5.268 5.32a1.448 1.448 0 0 0-1.018-.422h-.9a.306.306 0 0 1-.109-.021L1.623 6.496c-.83.83-.83 2.177 0 3.008l1.618 1.618a.305.305 0 0 1 .108-.022h.901c.38 0 .75-.153 1.018-.421L7.375 8.57a1.034 1.034 0 0 1 1.426 0l2.1 2.1c.267.268.638.421 1.017.421h.733c.04 0 .079.01.114.024l1.612-1.612c.83-.83.83-2.178 0-3.008z"/>
    </g>
  </svg>
);

export const CardTerminalIcon = ({ size = 24 }: { size?: number }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path d="M4.84 1.306h.115l.383-.002h.275l.901-.003h.312l1.463-.002q.842 0 1.684-.004.652-.002 1.305-.002l.778-.002h.733l.267-.001c.69-.005 1.39.047 2.01.383l.093.048a3.6 3.6 0 0 1 1.718 2.123 4.6 4.6 0 0 1 .144 1.268v.137l-.004.33.142-.002 1.33-.008.684-.004q.33-.004.661-.004.126 0 .251-.002c.732-.009 1.405.079 1.97.593.442.46.65 1.011.647 1.645l.001.113v.373l.001.27.002.727v.456l.001 1.592.003 1.48.001 1.273.002.76v.975c.005.733-.082 1.408-.596 1.972a2.26 2.26 0 0 1-1.65.646h-.115l-.368-.001h-.257l-.673-.003-.688-.002q-.675 0-1.349-.004l.002.183v.24l.001.12c.003 1.038-.346 1.959-1.058 2.715a3.48 3.48 0 0 1-2.417 1.015h-.602l-.275.001-.743.001h-.465l-1.458.002-1.678.002-1.3.002-.775.001h-.73l-.266.001c-1.114.006-2.06-.248-2.893-1.032-.628-.632-1.052-1.491-1.054-2.395v-1.03l-.002-.551v-1.205l-.001-.753-.002-2.22v-.542q0-1.089-.002-2.177l-.002-2.237V7.31q-.002-.535-.001-1.07v-.544q-.002-.25 0-.5l-.002-.264c.006-1.047.402-1.947 1.144-2.682a3.54 3.54 0 0 1 1.734-.89l.124-.025a4 4 0 0 1 .544-.03M3.196 3.61c-.31.38-.436.856-.436 1.34v.324l-.001.18v.496l-.001.534-.001 1.169-.001.73-.001 2.021v.656l-.002 2.11-.002 2.17-.001 1.216-.001 1.036v1.103l-.001.166c.004.773.222 1.32.768 1.861.433.4.965.523 1.542.522h.11l.365.001h.262l.711.002h.445q.696.002 1.393.002l1.605.002q.621.002 1.243.002l.74.001h.699l.255.001c.752.004 1.418-.067 2.002-.595.5-.527.644-1.054.642-1.762v-.693l.002-.548v-1.074l.002-.872v-2.746l.003-2.164.002-2.224.001-1.248V5.724q.002-.294 0-.589l.002-.17c-.005-.658-.224-1.145-.665-1.628-.595-.56-1.33-.585-2.096-.584h-.262l-.71-.001H9.817L8.374 2.75l-1.241-.001h-.74q-.348-.002-.696 0l-.255-.001c-.935-.004-1.593.155-2.246.86m13.821 3.424v2.813h4.22l.008-1.081.004-.34.001-.269.003-.139c0-.3-.024-.545-.226-.784-.254-.196-.44-.207-.752-.205h-.698l-.636.002h-.764zm0 4.266v5.673l1.782.011.56.005.335.001.222.002c.573.018.573.018 1.086-.207a.95.95 0 0 0 .24-.682V15.2l-.001-.337-.001-.886-.001-1.063-.002-1.615z"/>
    <path d="M5.567 4.165h.097l.32-.001h.23l.753-.003h.26l1.22-.002 1.406-.004 1.088-.002.65-.002q.306-.002.611 0l.224-.002c.55-.004.953.06 1.366.442.232.267.37.6.373.954v.12l.001.131.001.14.002.454v.158l.003.825q0 .425.004.85l.001.656.002.313c.01 1.268.01 1.268-.443 1.744-.313.27-.612.368-1.018.369h-.096l-.318.001-.228.001-.75.002h-.257l-1.213.003q-.699 0-1.398.003l-1.082.003-.645.001q-.304.002-.608.001l-.223.001c-.56.005-.948-.065-1.366-.455-.327-.34-.376-.677-.375-1.128l-.001-.145-.001-.473-.001-.33-.001-.691-.003-.885-.001-.681-.001-.326V5.75l-.002-.135c.004-.435.155-.76.45-1.077.3-.28.563-.371.97-.373m.058 1.46v4.22h7.078v-4.22zm2.437 11.346h2.204v1.453H8.063zm0-3.563h2.204v1.453H8.063zm3.938 3.563h2.157v1.453H12zm-7.829 0H6.33v1.453H4.172zm7.83-3.563h2.156v1.453H12zm-7.83 0H6.33v1.453H4.172z"/>
  </svg>
);

export const CashOnDeliveryIcon = ({ size = 24 }: { size?: number }) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 24 24"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g transform="scale(0.5)">
      <path d="M46.288 25.518c0.863 0.69 1.481 1.61 1.712 2.701 0.121 1.506 -0.06 2.684 -0.938 3.938 -0.323 0.367 -0.669 0.709 -1.02 1.049q-0.136 0.134 -0.271 0.269c-0.376 0.37 -0.753 0.734 -1.153 1.078 -0.46 0.395 -0.881 0.829 -1.306 1.261 -0.485 0.492 -0.97 0.972 -1.495 1.422 -0.394 0.351 -0.76 0.733 -1.13 1.109 -0.485 0.492 -0.97 0.972 -1.495 1.422 -0.394 0.351 -0.76 0.733 -1.13 1.109 -0.889 0.903 -0.889 0.903 -1.312 1.265 -0.465 0.398 -0.889 0.836 -1.319 1.272a238.781 238.781 0 0 1 -0.484 0.49l-0.213 0.216L34.5 44.344l-0.109 0.142c-0.312 0.351 -0.602 0.455 -1.069 0.49 -0.373 0.015 -0.745 0.018 -1.118 0.018q-0.253 0.003 -0.505 0.007c-0.458 0.007 -0.915 0.009 -1.373 0.011q-0.432 0.002 -0.864 0.005 -1.517 0.011 -3.035 0.013c-0.83 0.001 -1.659 0.008 -2.488 0.019 -8.644 0.116 -8.644 0.116 -11.908 -1.459 -0.671 -0.318 -1.385 -0.528 -2.093 -0.747l-0.132 0.267C9.238 44.256 8.532 44.999 7.313 45.469c-2.365 0.591 -4.874 0.188 -7.313 0.188V24.844c7.647 0 7.647 0 8.906 1.125 0.211 0.237 0.392 0.482 0.563 0.75l0.188 0.281v0.281l0.234 -0.144c2.356 -1.417 5.102 -1.93 7.801 -1.317 1.648 0.421 3.422 1.245 4.565 2.549 0.264 0.231 0.616 0.17 0.95 0.168l0.248 0.002q0.266 0.001 0.533 0.001c0.282 0 0.564 0.001 0.846 0.003 0.599 0.003 1.199 0.005 1.798 0.006 0.692 0.001 1.385 0.003 2.077 0.007 0.277 0.001 0.553 0.001 0.83 0 1.591 0.005 2.711 0.296 3.902 1.406C34.219 30.777 34.219 30.777 34.219 31.219c0.491 -0.423 0.971 -0.856 1.447 -1.295q0.446 -0.41 0.897 -0.815c0.346 -0.31 0.689 -0.623 1.031 -0.938q0.626 -0.575 1.26 -1.142a35.531 35.531 0 0 0 0.516 -0.476c2.029 -1.905 4.449 -2.765 6.919 -1.036M2.813 27.75v15c2.266 0.295 2.266 0.295 4.157 -0.5 0.284 -0.481 0.206 -1.084 0.202 -1.622q0 -0.196 0.002 -0.391c0.001 -0.353 0 -0.707 -0.001 -1.06 -0.001 -0.37 -0.001 -0.74 0 -1.11q0 -0.932 -0.003 -1.864c-0.003 -0.718 -0.003 -1.436 -0.002 -2.154q0.001 -1.037 -0.001 -2.074 -0.001 -0.441 0 -0.882q0 -0.519 -0.003 -1.039c-0.001 -0.188 0 -0.375 0.001 -0.562l-0.003 -0.339 -0.001 -0.294c-0.076 -0.562 -0.344 -0.755 -0.786 -1.108zm38.575 0.994c-0.199 0.188 -0.402 0.37 -0.607 0.552 -0.346 0.31 -0.689 0.623 -1.031 0.938 -0.572 0.525 -1.149 1.045 -1.727 1.563a169.125 169.125 0 0 0 -1.417 1.288A106.406 106.406 0 0 1 35.719 33.891l-0.228 0.206a44.156 44.156 0 0 1 -0.519 0.458c-0.585 0.514 -1.039 1.03 -1.495 1.664 -0.272 0.349 -0.567 0.587 -0.945 0.813l-0.177 0.107c-1.146 0.627 -2.343 0.594 -3.612 0.598q-0.291 0.002 -0.582 0.005c-0.508 0.004 -1.015 0.007 -1.522 0.01 -0.519 0.003 -1.038 0.007 -1.557 0.011 -1.016 0.008 -2.032 0.014 -3.049 0.02v-2.906l0.766 0.005c0.835 0.005 1.67 0.008 2.506 0.01 0.506 0.002 1.013 0.003 1.519 0.007 0.489 0.003 0.977 0.005 1.466 0.006q0.279 0.001 0.559 0.003c0.261 0.002 0.522 0.002 0.784 0.002l0.233 0.003c0.535 -0.003 0.929 -0.101 1.339 -0.471 0.387 -0.47 0.603 -0.795 0.579 -1.422 -0.065 -0.515 -0.272 -0.887 -0.656 -1.236 -0.499 -0.317 -0.908 -0.317 -1.479 -0.304l-0.275 0.003q-0.295 0.003 -0.59 0.007c-0.312 0.004 -0.624 0.006 -0.936 0.007 -0.886 0.003 -1.773 0.007 -2.659 0.021 -0.543 0.008 -1.086 0.011 -1.629 0.01q-0.309 0.001 -0.618 0.008c-1.698 0.039 -1.698 0.039 -2.19 -0.372 -0.265 -0.268 -0.469 -0.573 -0.686 -0.881 -1.42 -1.249 -3.195 -1.867 -5.071 -1.811C13.153 28.62 11.524 29.334 10.219 30.656c-0.248 0.372 -0.212 0.691 -0.209 1.122v0.261c0 0.285 0.002 0.569 0.003 0.854l0.001 0.592c0.001 0.519 0.003 1.038 0.005 1.558 0.002 0.53 0.003 1.059 0.004 1.589 0.002 1.04 0.005 2.079 0.009 3.119q0.792 0.277 1.583 0.554a734.813 734.813 0 0 1 0.793 0.278l0.261 0.092q0.27 0.094 0.541 0.186c0.481 0.163 0.954 0.335 1.424 0.528 1.801 0.709 3.498 0.746 5.408 0.742 0.349 0 0.698 0.002 1.046 0.004 0.823 0.005 1.646 0.005 2.469 0.003 0.844 -0.002 1.689 0.003 2.533 0.013 0.731 0.008 1.462 0.011 2.193 0.009 0.434 -0.001 0.868 0 1.302 0.007 1.996 0.116 1.996 0.116 3.761 -0.623a5.119 5.119 0 0 0 0.574 -0.697c0.164 -0.231 0.365 -0.418 0.571 -0.611a128.438 128.438 0 0 0 0.378 -0.384c0.363 -0.37 0.726 -0.735 1.119 -1.073 0.466 -0.401 0.893 -0.839 1.324 -1.277 0.485 -0.492 0.97 -0.972 1.495 -1.422 0.394 -0.351 0.76 -0.733 1.13 -1.109 0.485 -0.492 0.97 -0.972 1.495 -1.422 0.394 -0.351 0.76 -0.733 1.13 -1.109 0.495 -0.503 0.992 -0.993 1.527 -1.453 0.208 -0.183 0.394 -0.379 0.576 -0.587l0.187 -0.209c0.337 -0.433 0.303 -0.979 0.241 -1.501 -0.2 -0.52 -0.526 -0.886 -1.031 -1.125 -1.215 -0.312 -1.852 0.396 -2.675 1.182M17.906 2.344h20.719v20.719H17.906zm2.906 2.906v14.906h14.906V5.25h-3v6.188h-8.906V5.25zm5.906 0v3.281h3.094V5.25z"/>
    </g>
  </svg>
);
