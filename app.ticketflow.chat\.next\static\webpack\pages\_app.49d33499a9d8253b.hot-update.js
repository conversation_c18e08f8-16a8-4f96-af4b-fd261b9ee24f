"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ExtendedApp; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chatscope_chat_ui_kit_styles_dist_default_styles_min_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chatscope/chat-ui-kit-styles/dist/default/styles.min.css */ \"./node_modules/@chatscope/chat-ui-kit-styles/dist/default/styles.min.css\");\n/* harmony import */ var _chatscope_chat_ui_kit_styles_dist_default_styles_min_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_chatscope_chat_ui_kit_styles_dist_default_styles_min_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _styles_chat_scss__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/_chat.scss */ \"./styles/_chat.scss\");\n/* harmony import */ var _styles_chat_scss__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_chat_scss__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_app__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/app */ \"./node_modules/next/app.js\");\n/* harmony import */ var next_app__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_app__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var containers_layout_layout__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! containers/layout/layout */ \"./containers/layout/layout.tsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var utils_session__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! utils/session */ \"./utils/session.ts\");\n/* harmony import */ var containers_main_mainContainer__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! containers/main/mainContainer */ \"./containers/main/mainContainer.tsx\");\n/* harmony import */ var contexts_theme_theme_provider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! contexts/theme/theme.provider */ \"./contexts/theme/theme.provider.tsx\");\n/* harmony import */ var utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! utils/createEmotionCache */ \"./utils/createEmotionCache.ts\");\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @emotion/react */ \"./node_modules/@emotion/react/dist/emotion-react.browser.esm.js\");\n/* harmony import */ var contexts_muiTheme_muiTheme_provider__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! contexts/muiTheme/muiTheme.provider */ \"./contexts/muiTheme/muiTheme.provider.tsx\");\n/* harmony import */ var utils_useDeviceType__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! utils/useDeviceType */ \"./utils/useDeviceType.ts\");\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-query */ \"./node_modules/react-query/es/index.js\");\n/* harmony import */ var contexts_auth_auth_provider__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! contexts/auth/auth.provider */ \"./contexts/auth/auth.provider.tsx\");\n/* harmony import */ var contexts_settings_settings_provider__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! contexts/settings/settings.provider */ \"./contexts/settings/settings.provider.tsx\");\n/* harmony import */ var nprogress__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! nprogress */ \"./node_modules/nprogress/nprogress.js\");\n/* harmony import */ var nprogress__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(nprogress__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! react-redux */ \"./node_modules/react-redux/es/index.js\");\n/* harmony import */ var redux_store__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! redux/store */ \"./redux/store.ts\");\n/* harmony import */ var redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! redux-persist/integration/react */ \"./node_modules/redux-persist/es/integration/react.js\");\n/* harmony import */ var _i18n__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ../i18n */ \"./i18n.ts\");\n/* harmony import */ var utils_getLanguage__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! utils/getLanguage */ \"./utils/getLanguage.ts\");\n/* harmony import */ var constants_reactQuery_config__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! constants/reactQuery.config */ \"./constants/reactQuery.config.ts\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/script */ \"./node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var constants_constants__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! constants/constants */ \"./constants/constants.ts\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"./node_modules/react-toastify/dist/ReactToastify.css\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_27___default = /*#__PURE__*/__webpack_require__.n(react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_27__);\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! swiper/css */ \"./node_modules/swiper/swiper.min.css\");\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_28___default = /*#__PURE__*/__webpack_require__.n(swiper_css__WEBPACK_IMPORTED_MODULE_28__);\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! swiper/css/navigation */ \"./node_modules/swiper/modules/navigation/navigation.min.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_29___default = /*#__PURE__*/__webpack_require__.n(swiper_css_navigation__WEBPACK_IMPORTED_MODULE_29__);\n/* harmony import */ var nprogress_nprogress_css__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! nprogress/nprogress.css */ \"./node_modules/nprogress/nprogress.css\");\n/* harmony import */ var nprogress_nprogress_css__WEBPACK_IMPORTED_MODULE_30___default = /*#__PURE__*/__webpack_require__.n(nprogress_nprogress_css__WEBPACK_IMPORTED_MODULE_30__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nnext_router__WEBPACK_IMPORTED_MODULE_8__.Router.events.on(\"routeChangeStart\", ()=>nprogress__WEBPACK_IMPORTED_MODULE_18___default().start());\nnext_router__WEBPACK_IMPORTED_MODULE_8__.Router.events.on(\"routeChangeComplete\", ()=>nprogress__WEBPACK_IMPORTED_MODULE_18___default().done());\nnext_router__WEBPACK_IMPORTED_MODULE_8__.Router.events.on(\"routeChangeError\", ()=>nprogress__WEBPACK_IMPORTED_MODULE_18___default().done());\nconst clientSideEmotionCache = (0,utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_12__.createEmotionCache)();\nconst clientSideRtlEmotionCache = (0,utils_createEmotionCache__WEBPACK_IMPORTED_MODULE_12__.createRtlEmotionCache)();\nconst pagesWithoutLayout = [\n    \"register\",\n    \"login\",\n    \"reset-password\",\n    \"verify-phone\",\n    \"update-password\",\n    \"update-details\",\n    \"welcome\"\n];\nconst uiTypes = [\n    \"1\",\n    \"2\",\n    \"3\",\n    \"4\"\n];\nfunction ExtendedApp(param) {\n    let { Component , pageProps , userAgent , appTheme , emotionCache , authState , settingsState , defaultAddress , locale , appDirection , uiType  } = param;\n    _s();\n    nprogress__WEBPACK_IMPORTED_MODULE_18___default().configure({\n        showSpinner: false\n    });\n    const { pathname  } = (0,next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const isAuthPage = pagesWithoutLayout.some((item)=>pathname.includes(item));\n    const deviceType = (0,utils_useDeviceType__WEBPACK_IMPORTED_MODULE_14__.useDeviceType)(userAgent);\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(()=>new react_query__WEBPACK_IMPORTED_MODULE_15__.QueryClient(constants_reactQuery_config__WEBPACK_IMPORTED_MODULE_24__.config));\n    const csEmotionCache = appDirection === \"rtl\" ? clientSideRtlEmotionCache : clientSideEmotionCache;\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        _i18n__WEBPACK_IMPORTED_MODULE_22__[\"default\"].changeLanguage(locale);\n    }, [\n        locale\n    ]);\n    // Handle chunk loading errors\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        const handleChunkError = (event)=>{\n            var ref, ref1;\n            if (((ref = event.message) === null || ref === void 0 ? void 0 : ref.includes(\"Loading chunk\")) || ((ref1 = event.message) === null || ref1 === void 0 ? void 0 : ref1.includes(\"Loading CSS chunk\"))) {\n                console.warn(\"Chunk loading error detected, reloading page...\");\n                window.location.reload();\n            }\n        };\n        const handleUnhandledRejection = (event)=>{\n            var ref, ref1, ref2, ref3;\n            if (((ref = event.reason) === null || ref === void 0 ? void 0 : (ref1 = ref.message) === null || ref1 === void 0 ? void 0 : ref1.includes(\"Loading chunk\")) || ((ref2 = event.reason) === null || ref2 === void 0 ? void 0 : (ref3 = ref2.message) === null || ref3 === void 0 ? void 0 : ref3.includes(\"Loading CSS chunk\"))) {\n                console.warn(\"Chunk loading promise rejection detected, reloading page...\");\n                window.location.reload();\n            }\n        };\n        window.addEventListener(\"error\", handleChunkError);\n        window.addEventListener(\"unhandledrejection\", handleUnhandledRejection);\n        return ()=>{\n            window.removeEventListener(\"error\", handleChunkError);\n            window.removeEventListener(\"unhandledrejection\", handleUnhandledRejection);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_15__.QueryClientProvider, {\n        client: queryClient,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_15__.Hydrate, {\n            state: pageProps.dehydratedState,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_emotion_react__WEBPACK_IMPORTED_MODULE_31__.CacheProvider, {\n                value: emotionCache || csEmotionCache,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(contexts_muiTheme_muiTheme_provider__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    deviceType: deviceType,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(contexts_theme_theme_provider__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        appTheme: appTheme,\n                        appDirection: appDirection,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_19__.Provider, {\n                                store: redux_store__WEBPACK_IMPORTED_MODULE_20__.store,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(contexts_settings_settings_provider__WEBPACK_IMPORTED_MODULE_17__.SettingsProvider, {\n                                        settingsState: settingsState,\n                                        defaultAddress: defaultAddress,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(contexts_auth_auth_provider__WEBPACK_IMPORTED_MODULE_16__.AuthProvider, {\n                                            authState: authState,\n                                            children: isAuthPage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(containers_main_mainContainer__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                locale: locale,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                                    uiType: uiType,\n                                                    ...pageProps\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_21__.PersistGate, {\n                                                loading: null,\n                                                persistor: redux_store__WEBPACK_IMPORTED_MODULE_20__.persistor,\n                                                children: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(containers_layout_layout__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        locale: locale,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                                            uiType: uiType,\n                                                            ...pageProps\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 27\n                                                    }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_7__.ToastContainer, {\n                                        position: \"top-right\",\n                                        autoClose: 5000,\n                                        hideProgressBar: true,\n                                        newestOnTop: false,\n                                        closeOnClick: true,\n                                        pauseOnFocusLoss: true,\n                                        draggable: true,\n                                        pauseOnHover: true,\n                                        closeButton: false,\n                                        className: \"toast-alert\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_25___default()), {\n                                src: \"https://www.googletagmanager.com/gtag/js?id=\".concat(constants_constants__WEBPACK_IMPORTED_MODULE_26__.G_TAG),\n                                strategy: \"afterInteractive\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_25___default()), {\n                                id: \"google-analytics\",\n                                strategy: \"afterInteractive\",\n                                children: \"\\n                window.dataLayer = window.dataLayer || [];\\n                function gtag(){window.dataLayer.push(arguments);}\\n                gtag('js', new Date());\\n\\n                gtag('config', '\".concat(constants_constants__WEBPACK_IMPORTED_MODULE_26__.G_TAG, \"');\\n              \")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\OSPanel\\\\home\\\\app.ticketflow.chat\\\\pages\\\\_app.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s(ExtendedApp, \"L9cspxPa34JFSCsjhOdnplF2DUI=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        utils_useDeviceType__WEBPACK_IMPORTED_MODULE_14__.useDeviceType\n    ];\n});\n_c = ExtendedApp;\nExtendedApp.getInitialProps = async (appContext)=>{\n    const appProps = await next_app__WEBPACK_IMPORTED_MODULE_5___default().getInitialProps(appContext);\n    const { req  } = appContext.ctx;\n    const userAgent = req ? req.headers[\"user-agent\"] : navigator.userAgent;\n    const appTheme = (0,utils_session__WEBPACK_IMPORTED_MODULE_9__.getCookie)(\"theme\", appContext.ctx);\n    const appDirection = (0,utils_session__WEBPACK_IMPORTED_MODULE_9__.getCookie)(\"dir\", appContext.ctx);\n    const authState = (0,utils_session__WEBPACK_IMPORTED_MODULE_9__.getCookie)(\"user\", appContext.ctx);\n    const settingsState = (0,utils_session__WEBPACK_IMPORTED_MODULE_9__.getCookie)(\"settings\", appContext.ctx);\n    const defaultAddress = (0,utils_session__WEBPACK_IMPORTED_MODULE_9__.getCookie)(\"address\", appContext.ctx);\n    const locale = (0,utils_getLanguage__WEBPACK_IMPORTED_MODULE_23__[\"default\"])((0,utils_session__WEBPACK_IMPORTED_MODULE_9__.getCookie)(\"locale\", appContext.ctx));\n    const uiType = uiTypes.find((type)=>{\n        var ref;\n        return type === ((ref = appContext.router.query) === null || ref === void 0 ? void 0 : ref.v);\n    }) || \"1\";\n    _i18n__WEBPACK_IMPORTED_MODULE_22__[\"default\"].changeLanguage(locale);\n    let props = {\n        ...appProps,\n        userAgent,\n        appTheme,\n        appDirection,\n        authState,\n        settingsState,\n        defaultAddress,\n        locale,\n        uiType\n    };\n    return props;\n};\nvar _c;\n$RefreshReg$(_c, \"ExtendedApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n"));

/***/ })

});