{"__meta": {"id": "X17c9eb2f27bbd402f25f0e794c8e69f7", "datetime": "2025-07-24 22:58:33", "utime": 1753408713.20745, "method": "GET", "uri": "/api/v1/rest/shops/501?lang=pt-BR", "ip": "127.0.0.1"}, "php": {"version": "8.2.26", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[22:58:33] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753408713.109207, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753408712.872397, "end": 1753408713.207467, "duration": 0.3350701332092285, "duration_str": "335ms", "measures": [{"label": "Booting", "start": 1753408712.872397, "relative_start": 0, "end": 1753408713.093498, "relative_end": 1753408713.093498, "duration": 0.2211010456085205, "duration_str": "221ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753408713.093508, "relative_start": 0.22111105918884277, "end": 1753408713.20747, "relative_end": 2.86102294921875e-06, "duration": 0.11396193504333496, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 42580328, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/v1/rest/shops/{uuid}", "middleware": "api, block.ip", "controller": "App\\Http\\Controllers\\API\\v1\\Rest\\ShopController@show", "namespace": null, "prefix": "api/v1/rest", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\OSPanel\\home\\api.ticketflow.chat\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php&line=94\">\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php:94-117</a>"}, "queries": {"nb_statements": 15, "nb_failed_statements": 0, "accumulated_duration": 0.022600000000000006, "accumulated_duration_str": "22.6ms", "statements": [{"sql": "select `locale`, `default` from `languages` where `default` = 1 and `languages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 51}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.01709, "duration_str": "17.09ms", "stmt_id": "\\app\\Repositories\\CoreRepository.php:51", "connection": "foodyman", "start_percent": 0, "width_percent": 75.619}, {"sql": "select `id`, `default` from `currencies` where `default` = 1 and `currencies`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 41}, {"index": 16, "namespace": null, "name": "\\app\\Repositories\\CoreRepository.php", "line": 25}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 917}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 758}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 853}], "duration": 0.00041999999999999996, "duration_str": "420μs", "stmt_id": "\\app\\Repositories\\CoreRepository.php:41", "connection": "foodyman", "start_percent": 75.619, "width_percent": 1.858}, {"sql": "select `shops`.*, (select avg(`reviews`.`rating`) from `reviews` where `shops`.`id` = `reviews`.`assignable_id` and `reviews`.`assignable_type` = 'App\\Models\\Shop' and `reviews`.`deleted_at` is null) as `reviews_avg_rating`, (select count(*) from `reviews` where `shops`.`id` = `reviews`.`assignable_id` and `reviews`.`assignable_type` = 'App\\Models\\Shop' and `reviews`.`deleted_at` is null) as `reviews_count` from `shops` where `uuid` = '501' and `shops`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "App\\Models\\Shop", "501"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 241}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 96}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:241", "connection": "foodyman", "start_percent": 77.478, "width_percent": 3.009}, {"sql": "select `shops`.*, (select avg(`reviews`.`rating`) from `reviews` where `shops`.`id` = `reviews`.`assignable_id` and `reviews`.`assignable_type` = 'App\\Models\\Shop' and `reviews`.`deleted_at` is null) as `reviews_avg_rating`, (select count(*) from `reviews` where `shops`.`id` = `reviews`.`assignable_id` and `reviews`.`assignable_type` = 'App\\Models\\Shop' and `reviews`.`deleted_at` is null) as `reviews_count` from `shops` where `id` = 501 and `shops`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "App\\Models\\Shop", "501"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 247}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 96}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:247", "connection": "foodyman", "start_percent": 80.487, "width_percent": 2.832}, {"sql": "select * from `shop_translations` where `shop_translations`.`shop_id` in (501) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `shop_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 250}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 96}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:250", "connection": "foodyman", "start_percent": 83.319, "width_percent": 1.726}, {"sql": "select * from `shop_working_days` where `shop_working_days`.`shop_id` in (501) and `shop_working_days`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 250}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 96}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00032, "duration_str": "320μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:250", "connection": "foodyman", "start_percent": 85.044, "width_percent": 1.416}, {"sql": "select * from `shop_closed_dates` where `shop_closed_dates`.`shop_id` in (501) and `shop_closed_dates`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 250}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 96}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:250", "connection": "foodyman", "start_percent": 86.46, "width_percent": 1.195}, {"sql": "select * from `galleries` where `type` = 'shop-documents' and `galleries`.`loadable_id` in (501) and `galleries`.`loadable_type` = 'App\\Models\\Shop'", "type": "query", "params": [], "bindings": ["shop-documents", "App\\Models\\Shop"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 250}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 96}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:250", "connection": "foodyman", "start_percent": 87.655, "width_percent": 1.372}, {"sql": "select `bonusable_type`, `bonusable_id`, `bonus_quantity`, `bonus_stock_id`, `expired_at`, `value`, `type`, `status` from `bonuses` where `bonuses`.`bonusable_id` in (501) and `bonuses`.`bonusable_type` = 'App\\Models\\Shop' and `expired_at` > '2025-07-24 22:58:33' and `status` = 1 and `bonuses`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["App\\Models\\Shop", "2025-07-24 22:58:33", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 250}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 96}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00031, "duration_str": "310μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:250", "connection": "foodyman", "start_percent": 89.027, "width_percent": 1.372}, {"sql": "select `id`, `shop_id`, `end`, `active` from `discounts` where `discounts`.`shop_id` in (501) and `end` >= '2025-07-24 22:58:33' and `active` = 1 and `discounts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2025-07-24 22:58:33", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 250}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 96}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00046, "duration_str": "460μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:250", "connection": "foodyman", "start_percent": 90.398, "width_percent": 2.035}, {"sql": "select `id`, `payment_id`, `shop_id`, `status`, `client_id`, `secret_id` from `shop_payments` where `shop_payments`.`shop_id` in (501) and `shop_payments`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 250}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 96}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00027, "duration_str": "270μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:250", "connection": "foodyman", "start_percent": 92.434, "width_percent": 1.195}, {"sql": "select `categories`.*, `shop_categories`.`shop_id` as `pivot_shop_id`, `shop_categories`.`category_id` as `pivot_category_id` from `categories` inner join `shop_categories` on `categories`.`id` = `shop_categories`.`category_id` where `shop_categories`.`shop_id` in (501) and `categories`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 250}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 96}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:250", "connection": "foodyman", "start_percent": 93.628, "width_percent": 1.637}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (10, 12) and (`locale` = 'pt-BR' or `locale` = 'pt-BR') and `category_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pt-BR", "pt-BR"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "\\app\\Repositories\\ShopRepository\\ShopRepository.php", "line": 250}, {"index": 24, "namespace": null, "name": "\\app\\Http\\Controllers\\API\\v1\\Rest\\ShopController.php", "line": 96}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 45}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 261}], "duration": 0.00034, "duration_str": "340μs", "stmt_id": "\\app\\Repositories\\ShopRepository\\ShopRepository.php:250", "connection": "foodyman", "start_percent": 95.265, "width_percent": 1.504}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 501 and `orders`.`shop_id` is not null and (`shop_id` = 501 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["501", "501", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 96.77, "width_percent": 1.903}, {"sql": "select avg(`rate`) as aggregate from `orders` where `orders`.`shop_id` = 501 and `orders`.`shop_id` is not null and (`shop_id` = 501 and `status` = 'delivered')", "type": "query", "params": [], "bindings": ["501", "501", "delivered"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Shop.php", "line": 204}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 140}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Resources\\ShopResource.php", "line": 54}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 94}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 242}], "duration": 0.0003, "duration_str": "300μs", "stmt_id": "\\app\\Models\\Shop.php:204", "connection": "foodyman", "start_percent": 98.673, "width_percent": 1.327}]}, "models": {"data": {"App\\Models\\CategoryTranslation": 2, "App\\Models\\Category": 2, "App\\Models\\ShopWorkingDay": 7, "App\\Models\\ShopTranslation": 1, "App\\Models\\Shop": 1, "App\\Models\\Currency": 1, "App\\Models\\Language": 1}, "count": 15}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f78ba59-886c-4366-b557-4b5b84ef2ce9\" target=\"_blank\">View in Telescope</a>", "path_info": "/api/v1/rest/shops/501", "status_code": "<pre class=sf-dump id=sf-dump-805546535 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-805546535\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2039287230 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2039287230\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1082052173 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"5 characters\">pt-BR</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082052173\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-153299534 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">axios/1.4.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">gzip, compress, deflate, br</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-153299534\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1204799776 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"42 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58261</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.26 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/api/v1/rest/shops/501?lang=pt-BR</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"52 characters\">C:\\OSPanel\\home\\api.ticketflow.chat\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"22 characters\">/api/v1/rest/shops/501</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"32 characters\">/index.php/api/v1/rest/shops/501</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"<span class=sf-dump-str title=\"10 characters\">lang=pt-BR</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"11 characters\">axios/1.4.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"27 characters\">gzip, compress, deflate, br</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753408712.8724</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753408712</span>\n  \"<span class=sf-dump-key>argv</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">lang=pt-BR</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>argc</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1204799776\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1904347907 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1904347907\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1602792167 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 25 Jul 2025 01:58:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>5000</span>\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>4991</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1602792167\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-638369631 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-638369631\", {\"maxDepth\":0})</script>\n"}}